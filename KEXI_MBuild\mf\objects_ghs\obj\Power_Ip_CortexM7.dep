objects_ghs/obj/Power_Ip_CortexM7.o: \
 ../../SRC/HSW/MCAL_Static/Mcu_TS_T40D11M50I0R0/src/Power_Ip_CortexM7.c \
 ../../SRC/HSW/MCAL_Static/Mcu_TS_T40D11M50I0R0/include/Power_Ip_Private.h \
 ../../SRC/HSW/MCAL_Static/Mcu_TS_T40D11M50I0R0/include/Power_Ip_Types.h \
 ../../SRC/HSW/MCAL_Static/Mcu_TS_T40D11M50I0R0/include/Power_Ip_MC_ME_Types.h \
 ../../SRC/HSW/MCAL_Static/Mcu_TS_T40D11M50I0R0/include/Power_Ip_Specific.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Power_Ip_Cfg_Defines.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/StandardTypes.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Std_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Platform_Types.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Compiler.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Compiler_Cfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/CompilerDefinition.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_MC_ME.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_COMMON.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_ArchCfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_M7_COMMON.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/BasicTypes.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_MC_RGM.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_PMC.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_RESET.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_SCB.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_MSCM.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_A53_GPR.h \
 ../../SRC/HSW/MCAL_Static/Mcu_TS_T40D11M50I0R0/include/Power_Ip_MC_RGM_Types.h \
 ../../SRC/HSW/MCAL_Static/Mcu_TS_T40D11M50I0R0/include/Power_Ip_PMC_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Power_Ip_Cfg.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Power_Ip_VS_0_PBcfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Mcu_MemMap.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Power_Ip_VS_Headless_PBcfg.h \
 ../../SRC/HSW/MCAL_Static/Mcu_TS_T40D11M50I0R0/include/Power_Ip_CortexM7.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Mcal.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/DeviceDefinition.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Soc_Ips.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/IpVersionMacros.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Reg_eSys.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/OsIf_Internal.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_Cfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_SYSTICK.h
