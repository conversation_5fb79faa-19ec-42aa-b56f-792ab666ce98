#====================================================================================================
#
#    @file        config.mak
#    @version     1.1.0
#
#    @brief       Build configuration file
#    @details     This file contains macro's for make system
#
#====================================================================================================

## Definition of variable##
#The Name of Project: A02,AH8,A19,AY3,X4T_CL_3,X66_XL_3,etc.
PRJ_NAME := YL2

#Type of toolchain used for build: ghs, gcc, iar, diab, ect.
TOOLCHAIN := ghs

#Root directory of the toolchain used
GCC_DIR := C:/NXP/S32DS.3.4/S32DS/build_tools/gcc_v9.2/gcc-9.2-arm32-eabi
# GCC_DIR := C:/NXP/S32DS.3.5/S32DS/build_tools/gcc_v10.2/gcc-10.2-arm32-eabi/bin
# GCC_DIR := C:/yt_config_tool/gcc-arm-none-eabi-10.3-2021.10
GHS_DIR := D:/soft/ghs/comp_202314
IAR_DIR := C:/IAR_Systems/Embedded_Workbench_8.2/arm

#The Platform of the device:S32K1xx, Z20K1xx,ect.
PLATFORM := S32G3
