objects_ghs/obj/Xrdc_Ip_VS_Headless_PBcfg.o: \
 ../../SRC/HSW/MCAL_Cfg/generated/src/Xrdc_Ip_VS_Headless_PBcfg.c \
 ../../SRC/HSW/MCAL_Static/Rm_TS_T40D11M50I0R0/include/Xrdc_Ip.h \
 ../../SRC/BSW/cCore/Rte_TS_T40D11M50I0R0/include/SchM_Rm.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Rte_MemMap.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/CompilerDefinition.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Xrdc_Ip_Cfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Mcal.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Compiler.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Compiler_Cfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Std_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Platform_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/DeviceDefinition.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Soc_Ips.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/IpVersionMacros.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Reg_eSys.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/OsIf_Internal.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_Cfg.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_ArchCfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_SYSTICK.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_COMMON.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_M7_COMMON.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/BasicTypes.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Xrdc_Ip_VS_0_PBcfg.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Xrdc_Ip_VS_Headless_PBcfg.h \
 ../../SRC/HSW/MCAL_Static/Rm_TS_T40D11M50I0R0/include/Xrdc_Ip_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Xrdc_Ip_Cfg_Defines.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/StandardTypes.h \
 ../../SRC/HSW/MCAL_Static/Rm_TS_T40D11M50I0R0/include/Xrdc_Ip_Device_Registers.h
