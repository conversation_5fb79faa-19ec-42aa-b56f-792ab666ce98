objects_ghs/obj/Ctu_Ip.o: \
 ../../SRC/HSW/MCAL_Static/Adc_TS_T40D11M50I0R0/src/Ctu_Ip.c \
 ../../SRC/HSW/MCAL_Static/Adc_TS_T40D11M50I0R0/include/Ctu_Ip.h \
 ../../SRC/HSW/MCAL_Static/Adc_TS_T40D11M50I0R0/include/Ctu_Ip_Types.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/StandardTypes.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Std_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Platform_Types.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Compiler.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Compiler_Cfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/CompilerDefinition.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Ctu_Ip_CfgDefines.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_CTU.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_COMMON.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_ArchCfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_M7_COMMON.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/BasicTypes.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/OsIf.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/OsIf_Internal.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Soc_Ips.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/IpVersionMacros.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_Cfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_SYSTICK.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/BaseNXP_MemMap.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Ctu_Ip_Cfg.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Ctu_Ip_VS_0_PBcfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Adc_MemMap.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Ctu_Ip_VS_Headless_PBcfg.h \
 ../../SRC/HSW/MCAL_Static/Adc_TS_T40D11M50I0R0/include/Ctu_Ip_Irq.h \
 ../../SRC/HSW/MCAL_Static/Adc_TS_T40D11M50I0R0/include/Ctu_Ip_TrustedFunctions.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Reg_eSys.h \
 ../../SRC/BSW/cCore/Rte_TS_T40D11M50I0R0/include/SchM_Adc.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Rte_MemMap.h
