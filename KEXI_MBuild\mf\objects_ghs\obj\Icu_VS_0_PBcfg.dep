objects_ghs/obj/Icu_VS_0_PBcfg.o: \
 ../../SRC/HSW/MCAL_Cfg/generated/src/Icu_VS_0_PBcfg.c \
 ../../SRC/HSW/MCAL_Static/Icu_TS_T40D11M50I0R0/include/Icu.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Mcal.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Compiler.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Compiler_Cfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/CompilerDefinition.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Std_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Platform_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/DeviceDefinition.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Soc_Ips.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/IpVersionMacros.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Reg_eSys.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/OsIf_Internal.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_Cfg.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/OsIf_ArchCfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_SYSTICK.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_COMMON.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_M7_COMMON.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/BasicTypes.h \
 ../../SRC/HSW/MCAL_Static/Icu_TS_T40D11M50I0R0/include/Icu_Irq.h \
 ../../SRC/HSW/MCAL_Static/Icu_TS_T40D11M50I0R0/include/Icu_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Icu_Cfg.h \
 ../../SRC/HSW/MCAL_Static/Icu_TS_T40D11M50I0R0/include/Icu_EnvCfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/StandardTypes.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Icu_VS_0_PBcfg.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Icu_VS_Headless_PBcfg.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/include/Icu_MemMap.h \
 ../../SRC/HSW/MCAL_Static/Icu_TS_T40D11M50I0R0/include/Icu_Ipw_Types.h \
 ../../SRC/HSW/MCAL_Static/Icu_TS_T40D11M50I0R0/include/Ftm_Icu_Ip_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Ftm_Icu_Ip_Defines.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_FTM.h \
 ../../SRC/HSW/MCAL_Static/Icu_TS_T40D11M50I0R0/include/Siul2_Icu_Ip_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Siul2_Icu_Ip_Defines.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_SIUL2.h \
 ../../SRC/HSW/MCAL_Static/Icu_TS_T40D11M50I0R0/include/Wkpu_Ip_Types.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Wkpu_Ip_Defines.h \
 ../../SRC/HSW/MCAL_Static/BaseNXP_TS_T40D11M50I0R0/header/S32G399A_WKPU.h \
 ../../SRC/HSW/MCAL_Cfg/generated/include/Icu_Ipw_VS_0_PBcfg.h
